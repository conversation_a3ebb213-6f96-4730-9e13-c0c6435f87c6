using CommandGuard.Models;

namespace CommandGuard.Interfaces;

/// <summary>
/// 期号时间服务接口
/// </summary>
public interface IIssueTimeService : IDisposable
{
    /// <summary>
    /// 创建期号时间数据
    /// </summary>
    /// <param name="issueTimeYear">目标年份的任意日期</param>
    /// <returns>创建的第一条期号时间记录</returns>
    Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear);

    /// <summary>
    /// 维护更新当前期号时间信息
    /// </summary>
    /// <param name="token">取消令牌，用于优雅停止服务</param>
    /// <returns>持续运行的异步任务</returns>
    Task UpdateCurrentIssueTimeAsync(CancellationToken token);

    /// <summary>
    /// 线程安全地获取当前缓存的期号时间信息
    /// </summary>
    /// <returns>当前缓存的期号时间对象，缓存为空时返回null</returns>
    IssueTime? GetCurrentCachedIssueTime();

    int GetCurrentCachedOpenTimeSpan();
    int GetCurrentCachedCloseTimeSpan();
    bool GetCurrentCachedCanBetStatus();

    /// <summary>
    /// 清除期号时间缓存
    /// </summary>
    void ClearCache();
}
using CommandGuard.Models;

namespace CommandGuard.Interfaces;

/// <summary>
/// 会员业务服务接口
///
/// 功能概述：
/// - 定义会员数据管理的核心业务接口
/// - 提供会员信息的查询、添加等基础操作
/// - 支持异步操作模式，提高系统响应性能
/// - 为彩票投注系统提供用户管理的标准接口
///
/// 设计原则：
/// - 接口隔离：只包含会员管理相关的核心方法
/// - 异步优先：所有方法都采用异步设计
/// - 职责单一：专注于会员数据的业务逻辑
/// - 扩展友好：便于后续功能扩展和实现替换
///
/// 实现要求：
/// - 所有方法必须是线程安全的
/// - 需要完整的异常处理和日志记录
/// - 支持事务处理和数据一致性
/// - 提供详细的操作审计日志
/// </summary>
public interface IMemberService
{
    /// <summary>
    /// 异步添加会员信息
    ///
    /// 功能：向系统中添加新的会员记录
    ///
    /// 业务规则：
    /// - 会员账号必须唯一
    /// - 必填字段不能为空
    /// - 财务信息必须有效
    /// - 拉手关系必须合法
    ///
    /// 返回值：
    /// - 成功：返回受影响的行数（通常为1）
    /// - 失败：抛出相应的业务异常
    /// </summary>
    /// <param name="member">要添加的会员对象</param>
    /// <returns>受影响的行数</returns>
    Task<int> AddMemberAsync(Member member);

    /// <summary>
    /// 根据账号异步获取会员信息
    ///
    /// 功能：通过会员账号查询完整的会员信息
    ///
    /// 查询特点：
    /// - 主键查询，性能最优
    /// - 返回完整的会员数据
    /// - 支持空结果处理
    ///
    /// 返回值：
    /// - 找到：返回完整的Member对象
    /// - 未找到：返回null
    /// - 异常：抛出相应异常
    /// </summary>
    /// <param name="account">会员账号（唯一标识）</param>
    /// <returns>会员对象或null</returns>
    Task<Member?> GetMemberByAccountAsync(string account);

    /// <summary>
    /// 异步获取所有会员列表
    ///
    /// 功能：查询系统中的所有会员记录
    ///
    /// 使用场景：
    /// - 管理界面数据展示
    /// - 数据统计和分析
    /// - 批量数据处理
    ///
    /// 注意事项：
    /// - 大数据量时考虑分页
    /// - 包含敏感财务信息
    /// - 需要适当的权限控制
    /// </summary>
    /// <returns>所有会员的列表</returns>
    Task<List<Member>> GetAllMemberAsync();
}
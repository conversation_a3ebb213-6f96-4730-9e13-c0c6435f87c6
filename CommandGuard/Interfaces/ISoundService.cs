namespace CommandGuard.Interfaces;

/// <summary>
/// 音效服务接口
/// 定义音效播放和管理的核心功能
/// 
/// 核心职责：
/// 1. 音效资源管理 - 加载和管理各种音效文件
/// 2. 音效播放控制 - 提供不同场景的音效播放功能
/// 3. 音效状态管理 - 管理音效播放器的生命周期
/// 
/// 支持的音效类型：
/// - 系统提示音：用于一般的系统操作反馈
/// - 警告音效：用于重要提醒和警告
/// - 成功音效：用于操作成功的反馈
/// - 错误音效：用于错误和异常的提醒
/// 
/// 设计特点：
/// - 资源预加载：系统启动时预加载所有音效文件
/// - 异步播放：音效播放不阻塞主线程
/// - 资源管理：自动管理音效播放器的生命周期
/// - 异常安全：音效播放失败不影响主系统运行
/// </summary>
public interface ISoundService
{
    Task PlayStartServiceAsync();
    Task PlayStopServiceAsync();
    Task PlayRechargeWithdrawalAsync();
    Task PlayBetSuccessAsync();
    Task PlayBetFalseAsync();
    Task PlayOpenDrawAsync();
    Task PlayStopBetAsync();
}
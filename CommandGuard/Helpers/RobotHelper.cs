using System.Collections.Concurrent;
using AiHelper;
using CommandGuard.Enums;
using CommandGuard.Models;

namespace CommandGuard.Helpers;

public static class RobotHelper
{
    public static RobotInfo RobotInfo { get; set; } = new();
    public static string WorkGroupId { get; set; } = string.Empty;
    public static ConcurrentDictionary<string, string> GroupDic { get; set; } = new();
    public static List<string> OpenTipsList { get; set; } = new();

    #region 根据不同彩种提取不同的开奖号码

    /// <summary>
    /// 根据不同彩种提取不同的开奖号码
    /// </summary>
    /// <param name="kj"></param>
    /// <param name="betLottery"></param>
    /// <returns></returns>
    public static async Task<string> GetDrawNumStr(KaiJiang kj, EnumBetLottery betLottery)
    {
        string drawNumStr = "";

        try
        {
            if (betLottery == EnumBetLottery.台湾宾果1)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum.Last();
            }
            else if (betLottery == EnumBetLottery.台湾宾果2)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[0] + "," + drawNum[^2] + "," + drawNum[^1];
            }
            else if (betLottery == EnumBetLottery.台湾宾果3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                for (int i = 0; i < drawNum.Length - 1; i++)
                {
                    drawNumStr += drawNum[i] + ",";
                }

                drawNumStr = drawNumStr.TrimEnd(',');
            }
            else if (betLottery == EnumBetLottery.一六八飞艇前3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[0] + "," + drawNum[1] + "," + drawNum[2];
            }
            else if (betLottery == EnumBetLottery.一六八飞艇中3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[4] + "," + drawNum[5] + "," + drawNum[6];
            }
            else if (betLottery == EnumBetLottery.一六八飞艇后3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumStr = drawNum[7] + "," + drawNum[8] + "," + drawNum[9];
            }
        }
        catch (Exception)
        {
            await Task.Delay(0);
        }

        return drawNumStr;
    }

    #endregion

    #region 根据不同彩种计算开奖号码的和

    /// <summary>
    /// 根据不同彩种计算开奖号码的和
    /// </summary>
    /// <returns></returns>
    public static async Task<int> GetDrawNumSum(string drawNumStr)
    {
        int sum = 0;

        try
        {
            string[] drawNum = Ai.Split(drawNumStr, ",");
            foreach (string num in drawNum)
            {
                sum += int.Parse(num);
            }
        }
        catch (Exception)
        {
            await Task.Delay(0);
        }

        return sum;
    }

    #endregion

    #region 根据不同彩种计算不同的开奖结果

    /// <summary>
    /// 根据不同彩种计算不同的开奖结果
    /// </summary>
    /// <returns></returns>
    public static async Task<int> GetDrawResult(int drawNumSum)
    {
        try
        {
            int drawResult = drawNumSum % 4;
            drawResult = drawResult == 0 ? 4 : drawResult;
            return drawResult;
        }
        catch (Exception)
        {
            await Task.Delay(0);
        }

        return -1;
    }

    #endregion
}
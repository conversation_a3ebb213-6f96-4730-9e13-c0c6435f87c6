<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Costura.Fody" Version="6.0.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Flurl.Http" Version="4.0.2"/>
        <PackageReference Include="FreeSql" Version="3.5.211"/>
        <PackageReference Include="FreeSql.DbContext" Version="3.5.211"/>
        <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.211"/>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
        <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0"/>
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0"/>
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0"/>
        <PackageReference Include="SunnyUI" Version="3.8.7"/>
    </ItemGroup>

    <ItemGroup>
      <Reference Include="AiHelper">
        <HintPath>D:\TreeDLL\AiHelper.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
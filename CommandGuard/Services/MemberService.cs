using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 会员业务服务实现类
///
/// 功能概述：
/// - 提供会员数据的完整CRUD操作功能
/// - 支持会员信息的查询、添加、更新和删除
/// - 集成完整的日志记录和异常处理机制
/// - 为彩票投注系统提供用户管理的核心服务
///
/// 核心职责：
/// 1. 会员数据管理 - 处理会员的基本信息维护
/// 2. 财务信息管理 - 管理会员余额、回水比例等财务数据
/// 3. 层级关系管理 - 处理拉手上级和返利关系
/// 4. 用户分类管理 - 区分真人用户和假人用户
///
/// 业务特点：
/// - 支持高并发的会员数据访问
/// - 提供线程安全的数据操作
/// - 集成详细的操作日志记录
/// - 支持复杂的会员查询条件
///
/// 使用场景：
/// - 用户注册和信息维护
/// - 投注前的用户身份验证
/// - 财务操作的用户数据获取
/// - 拉手层级关系的建立和查询
/// - 系统统计和数据分析
/// </summary>
public class MemberService : IMemberService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<MemberService> _logger;

    /// <summary>
    /// 数据库服务接口
    ///
    /// 功能：提供数据库访问能力和连接管理
    ///
    /// 核心能力：
    /// - FreeSql ORM实例访问
    /// - 数据库连接池管理
    /// - 事务处理支持
    /// - 线程安全的数据库操作锁
    ///
    /// 使用场景：
    /// - 会员数据的CRUD操作
    /// - 复杂查询和数据统计
    /// - 事务处理和批量操作
    /// - 数据库连接异常的处理
    ///
    /// 特点：
    /// - 通过依赖注入获取，确保单例使用
    /// - 不需要手动释放，由容器管理生命周期
    /// - 支持多种数据库类型（当前使用SQLite）
    /// - 提供线程安全的数据库操作锁
    /// </summary>
    private readonly IFreeSql _freeSql;

    #endregion

    #region 构造函数

    /// <summary>
    /// 会员服务构造函数
    /// </summary>
    /// <param name="logger">日志记录器，用于记录业务操作和异常信息</param>
    /// <param name="freeSql"></param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出此异常</exception>
    public MemberService(ILogger<MemberService> logger, IFreeSql freeSql)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));

        _logger.LogDebug("会员服务实例已创建，准备提供会员数据管理功能");
    }

    #endregion

    #region 公共方法 - 会员数据操作接口

    /// <summary>
    /// 异步添加会员信息
    ///
    /// 功能：向数据库中插入新的会员记录
    ///
    /// 业务流程：
    /// 1. 接收会员对象参数
    /// 2. 记录操作日志
    /// 3. 构建插入SQL语句
    /// 4. 执行数据库插入操作
    /// 5. 返回受影响的行数
    ///
    /// 数据验证：
    /// - 会员账号唯一性检查（数据库约束）
    /// - 必填字段完整性验证
    /// - 数据格式和长度验证
    ///
    /// 异常处理：
    /// - 主键冲突：会员账号已存在
    /// - 数据库连接异常
    /// - 数据格式验证失败
    /// - 外键约束违反（如拉手上级不存在）
    ///
    /// 性能考虑：
    /// - 使用异步操作避免阻塞
    /// - 支持批量插入优化
    /// - 自动参数化防止SQL注入
    ///
    /// 使用场景：
    /// - 新用户注册时创建会员记录
    /// - 管理员手动添加会员
    /// - 批量导入会员数据
    /// - 系统初始化时创建默认会员
    /// </summary>
    /// <param name="member">要添加的会员对象，包含完整的会员信息</param>
    /// <returns>返回受影响的行数，成功时通常为1</returns>
    /// <exception cref="ArgumentNullException">当member参数为null时抛出</exception>
    /// <exception cref="InvalidOperationException">当会员账号已存在时抛出</exception>
    public async Task<int> AddMemberAsync(Member member)
    {
        try
        {
            // 参数验证
            if (member == null)
            {
                _logger.LogWarning("尝试添加空的会员对象");
                throw new ArgumentNullException(nameof(member), @"会员对象不能为空");
            }

            _logger.LogInformation("开始添加会员：账号={Account}, 昵称={昵称}", member.Account, member.NickName);

            // 执行数据库插入操作
            int result = await _freeSql.Insert(member)
                .AppendData(member)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                _logger.LogInformation("成功添加会员：账号={Account}, 影响行数={AffectedRows}", member.Account, result);
            }
            else
            {
                _logger.LogWarning("添加会员失败，没有行受到影响：账号={Account}", member.Account);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加会员时发生异常：账号={Account}", member?.Account ?? "未知");
            throw; // 重新抛出异常，让调用方处理
        }
    }

    /// <summary>
    /// 根据账号异步获取会员信息
    ///
    /// 功能：通过会员账号（QQ号等）查询对应的会员详细信息
    ///
    /// 查询逻辑：
    /// - 使用会员账号作为主键进行精确匹配
    /// - 返回完整的会员信息对象
    /// - 支持空结果的安全处理
    ///
    /// 性能优化：
    /// - 主键查询，性能最优
    /// - 使用异步操作提高响应性
    /// - 自动利用数据库索引
    ///
    /// 业务场景：
    /// - 用户登录时验证身份
    /// - 投注前获取用户余额信息
    /// - 显示用户详细资料
    /// - 财务操作前的用户信息确认
    /// - 拉手关系查询和验证
    ///
    /// 返回数据：
    /// - 找到记录：返回完整的Member对象
    /// - 未找到记录：返回null
    /// - 查询异常：抛出相应异常
    ///
    /// 注意事项：
    /// - 调用方需要检查返回值是否为null
    /// - 账号参数应该进行格式验证
    /// - 支持大小写敏感的精确匹配
    /// </summary>
    /// <param name="account">要查询的账号（会员账号），作为唯一标识</param>
    /// <returns>返回匹配的会员对象，如果未找到则返回null</returns>
    /// <exception cref="ArgumentException">当account参数为空或格式无效时抛出</exception>
    public async Task<Member?> GetMemberByAccountAsync(string account)
    {
        try
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(account))
            {
                _logger.LogWarning("尝试使用空的账号查询会员");
                throw new ArgumentException(@"账号不能为空或空白字符", nameof(account));
            }

            _logger.LogDebug("开始查询会员：账号={Account}", account);

            // 执行数据库查询
            var member = await _freeSql.Select<Member>()
                .Where(a => a.Account == account)
                .FirstAsync();

            if (member != null)
            {
                _logger.LogDebug("成功找到会员：账号={Account}, 昵称={昵称}, 余额={Balance}",
                    member.Account, member.NickName, member.Balance);
                return member;
            }

            _logger.LogDebug("未找到指定账号的会员：账号={Account}", account);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据账号查询会员时发生异常：账号={Account}", account);
            throw; // 重新抛出异常，让调用方处理
        }
    }

    /// <summary>
    /// 异步获取所有会员列表
    ///
    /// 功能：查询数据库中的所有会员记录，返回完整的会员列表
    ///
    /// 查询特点：
    /// - 无条件查询，返回所有记录
    /// - 包含完整的会员信息字段
    /// - 支持大数据量的高效查询
    ///
    /// 性能考虑：
    /// - 使用异步操作避免阻塞
    /// - 数据量大时考虑分页处理
    /// - 自动优化查询执行计划
    ///
    /// 使用场景：
    /// - 管理界面显示会员列表
    /// - 数据统计和分析
    /// - 批量数据处理
    /// - 系统监控和审计
    /// - 会员数据导出功能
    ///
    /// 数据安全：
    /// - 返回的是数据副本，修改不影响数据库
    /// - 包含敏感的财务信息，需要权限控制
    /// - 支持数据脱敏处理
    ///
    /// 扩展建议：
    /// - 可添加分页参数支持大数据量
    /// - 可添加排序参数自定义排序方式
    /// - 可添加筛选条件支持条件查询
    /// - 可添加字段选择减少数据传输量
    ///
    /// 注意事项：
    /// - 大数据量时可能影响性能和内存
    /// - 建议在生产环境中添加分页限制
    /// - 考虑添加缓存机制提高查询效率
    /// </summary>
    /// <returns>返回包含所有会员信息的列表集合</returns>
    /// <exception cref="OutOfMemoryException">当数据量过大导致内存不足时抛出</exception>
    public async Task<List<Member>> GetAllMemberAsync()
    {
        try
        {
            _logger.LogDebug("开始查询所有会员列表");

            // 执行数据库查询，获取所有会员记录
            var memberList = await _freeSql.Select<Member>()
                .ToListAsync();

            _logger.LogInformation("成功获取会员列表，总数量：{Count} 个会员", memberList.Count);

            // 记录一些统计信息（不包含敏感数据）
            if (memberList.Count > 0)
            {
                var realMemberCount = memberList.Count(m => !m.IsFake);
                var fakeMemberCount = memberList.Count(m => m.IsFake);

                _logger.LogDebug("会员统计：真人用户 {RealCount} 个，假人用户 {FakeCount} 个",
                    realMemberCount, fakeMemberCount);
            }

            return memberList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有会员列表时发生异常");
            throw; // 重新抛出异常，让调用方处理
        }
    }

    #endregion
}
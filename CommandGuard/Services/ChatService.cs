using AiHelper;
using CommandGuard.ChatPlatform;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// Chat服务实现
/// </summary>
public class ChatService : IChatService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<ChatService> _logger;

    /// <summary>
    /// 数据库服务接口
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 自定义日志服务接口
    /// </summary>
    private readonly ICustomLogService _customLogService;

    #endregion

    #region 构造函数

    /// <summary>
    /// 会员服务构造函数
    /// </summary>
    /// <param name="logger">日志记录器，用于记录业务操作和异常信息</param>
    /// <param name="freeSql">数据库服务，提供数据访问和连接管理功能</param>
    /// <param name="customLogService"></param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出此异常</exception>
    public ChatService(ILogger<ChatService> logger, IFreeSql freeSql, ICustomLogService customLogService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _customLogService = customLogService ?? throw new ArgumentNullException(nameof(customLogService));
        _logger.LogDebug("Chat服务实例已创建，准备提供会员数据管理功能");
    }

    #endregion

    public async Task GetRobotInfoAsync()
    {
        try
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "GetRobotInfoAsync" + Ai.中括号右, "GetRobotInfoAsync");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.MyQQ:
                    await MyQqHelper.GetRobotInfoAsync();
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.GetRobotInfoAsync();
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetRobotInfoAsync");
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetRobotInfoError" + Ai.中括号右, ex.ToString());
        }
    }

    public async Task GetGroupDicAsync()
    {
        try
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "GetGroupDicAsync" + Ai.中括号右, "GetGroupDicAsync");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.MyQQ:
                    await MyQqHelper.GetGroupDicAsync();
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.GetGroupDicAsync();
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetGroupDicAsync");
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetGroupDicAsync" + Ai.中括号右, ex.ToString());
        }
    }

    public async Task GetNickNameAsync(string account)
    {
        try
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "GetNickNameAsync" + Ai.中括号右, "GetNickNameAsync");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.MyQQ:
                    await MyQqHelper.GetNickNameAsync(account);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.GetNickNameAsync(account);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetNickNameAsync");
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.GetNickNameAsync" + Ai.中括号右, ex.ToString());
        }
    }

    public async Task SendGroupMessageAsync(string message)
    {
        try
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "SendGroupMessageAsync" + Ai.中括号右, "SendGroupMessageAsync");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.MyQQ:
                    await MyQqHelper.SendGroupMessageAsync(message);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.SendGroupMessageAsync(message);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SendGroupMessageAsync");
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendGroupMessageAsync" + Ai.中括号右, ex.ToString());
        }
    }

    public async Task SendGroupMessageAsync(string message, string atAccount)
    {
        try
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "SendGroupMessageAsync" + Ai.中括号右, "SendGroupMessageAsync");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.MyQQ:
                    await MyQqHelper.SendGroupMessageAsync(message, atAccount);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.SendGroupMessageAsync(message, atAccount);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SendGroupMessageAsync");
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendGroupMessageAsync" + Ai.中括号右, ex.ToString());
        }
    }

    public async Task SendImageAsync(string imgPath)
    {
        try
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "SendImageAsync" + Ai.中括号右, "SendImageAsync");
            switch (CommonHelper.ChatApp)
            {
                case EnumChatApp.MyQQ:
                    await MyQqHelper.SendImageAsync("", imgPath);
                    break;

                case EnumChatApp.一起聊吧:
                    await OneChatHelper.SendImageAsync("", imgPath);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SendImageAsync");
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendImageAsync" + Ai.中括号右, ex.ToString());
        }
    }
}
using CommandGuard.Configuration;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Flurl.Http;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 开奖服务类 - 彩票开奖信息处理的核心业务服务
/// </summary>
public class DrawService : IDrawService
{
    #region 私有字段和依赖

    /// <summary>
    /// 日志记录器
    ///
    /// 功能：记录服务运行过程中的详细信息
    /// 日志级别：
    /// - Debug：缓存命中、状态检查等调试信息
    /// - Information：状态变化、数据创建等重要操作
    /// - Warning：异常恢复、数据不一致等警告
    /// - Error：数据库错误、计算异常等错误信息
    ///
    /// 日志内容：
    /// - 期号数据的创建和更新
    /// - 状态变化的详细记录
    /// - 性能指标和缓存命中率
    /// - 异常情况和恢复过程
    /// </summary>
    private readonly ILogger<DrawService> _logger;

    /// <summary>
    /// 数据库服务接口
    ///
    /// 功能：提供数据库访问能力和连接管理
    /// 核心能力：
    /// - FreeSql ORM实例访问
    /// - 数据库连接池管理
    /// - 事务处理支持
    /// - 线程安全的数据库操作锁
    ///
    /// 使用场景：
    /// - 期号数据的批量创建和查询
    /// - 数据库结构的自动同步
    /// - 复杂查询和数据统计
    /// - 数据库连接异常的处理
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 自定义日志服务接口
    /// </summary>
    private readonly ICustomLogService _customLogService;

    /// <summary>
    /// 开奖数据API地址列表
    /// 包含多个服务器地址，用于提高数据获取的可靠性
    /// </summary>
    private List<string> DataUrlList =>
    [
        $"{RobotSetting.Current.PlatformHost}/DrawInfo/"
    ];

    /// <summary>
    /// 最后开奖数据对象
    /// </summary>
    public KaiJiang LastKj { get; set; } = new();

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 开奖服务构造函数
    /// </summary>
    /// <param name="logger">日志记录器，用于记录服务运行状态和异常信息</param>
    /// <param name="freeSql"></param>
    /// <param name="customLogService"></param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public DrawService(ILogger<DrawService> logger, IFreeSql freeSql, ICustomLogService customLogService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _customLogService = customLogService ?? throw new ArgumentNullException(nameof(customLogService));
    }

    #endregion

    /// <summary>
    /// 获取开奖信息
    /// 获取开奖数据，并保存到数据库
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task<bool> GetDrawFromApiAsync(CancellationToken token)
    {
        bool hasNewDrawData = false;
        foreach (string url in DataUrlList)
        {
            try
            {
                // 发送请求获取数据
                ResObj resObj = await url.WithTimeout(TimeSpan.FromSeconds(3))
                    .PostJsonAsync(new
                    {
                        CommonHelper.Lottery
                    }, cancellationToken: token)
                    .ReceiveJson<ResObj>();

                // 获取数据异常，跳过
                if (resObj == null)
                {
                    continue;
                }

                // 获取数据异常，记录日志
                if (resObj.StatusCode != 0)
                {
                    await _customLogService.AddLogAsync(EnumLogType.机器人, "获取开奖信息异常", resObj.Msg);
                    continue;
                }

                // 没有数据，跳过
                if (!resObj.DrawList.Any())
                {
                    continue;
                }

                // 遍历取出数据
                List<KaiJiang> kjList = new List<KaiJiang>();
                foreach (Draw draw in resObj.DrawList)
                {
                    KaiJiang kj = new KaiJiang
                    {
                        Issue = draw.PreDrawIssue, // 期号
                        DrawNum = draw.PreDrawCode, // 开奖号码
                        Time = draw.PreDrawTime // 开奖时间
                    };
                    kjList.Add(kj);
                }

                // 按开奖期号排序kjList
                kjList = kjList.OrderBy(x => x.Issue).ToList();

                // 遍历写入数据库，避免重复数据
                foreach (KaiJiang kj in kjList)
                {
                    // 数据库中不存在该期号数据，写入数据库
                    KaiJiang? kjInDb = await _freeSql.Select<KaiJiang>()
                        .Where(a => a.Issue.Equals(kj.Issue))
                        .ToOneAsync(token);

                    if (kjInDb == null)
                    {
                        await _freeSql.Insert<KaiJiang>().AppendData(kj).ExecuteIdentityAsync(token);
                        hasNewDrawData = true;
                    }
                }
            }
            catch (FlurlHttpTimeoutException ex)
            {
                await _customLogService.AddLogAsync(EnumLogType.机器人, "获取开奖信息超时", ex.ToString());
                _logger.LogError(ex.ToString());
            }
            catch (Exception ex)
            {
                await _customLogService.AddLogAsync(EnumLogType.机器人, "获取开奖信息异常", ex.ToString());
                _logger.LogError(ex.ToString());
            }
        }

        return hasNewDrawData;
    }

    /// <summary>
    /// 获取开奖数量
    /// </summary>
    public async Task<bool> HasDrawAsync(CancellationToken token)
    {
        return await _freeSql.Select<KaiJiang>().AnyAsync(token).ConfigureAwait(false);
    }

    /// <summary>
    /// 获取最新开奖信息
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    public async Task<KaiJiang> GetDrawLastAsync(CancellationToken token)
    {
        var lastDraw = await _freeSql.Select<KaiJiang>()
            .OrderByDescending(x => x.Issue)
            .FirstAsync(token);
        return lastDraw;
    }

    /// <summary>
    /// 清空开奖信息
    /// </summary>
    public Task ClearDrawAsync(CancellationToken token)
    {
        _logger.LogInformation("清空开奖信息");
        return _freeSql.Delete<KaiJiang>().Where("1=1").ExecuteAffrowsAsync(token);
    }
}
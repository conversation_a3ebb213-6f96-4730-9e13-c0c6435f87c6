using CommandGuard.Enums;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 自定义日志服务
/// </summary>
public class CustomLogService : ICustomLogService
{
    #region 私有字段

    /// <summary>
    /// FreeSql ORM实例
    /// 用于执行数据库CRUD操作，提供强类型的数据访问能力
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 日志记录器
    /// 记录业务操作的详细信息，便于调试和监控
    /// </summary>
    private readonly ILogger<CustomLogService> _logger;

    #endregion

    #region 构造函数

    /// <summary>
    /// 人员服务构造函数
    /// 通过依赖注入获取所需的服务实例
    /// </summary>
    /// <param name="freeSql">FreeSql ORM实例，用于数据访问</param>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public CustomLogService(IFreeSql freeSql, ILogger<CustomLogService> logger)
    {
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 添加日志
    /// </summary>
    /// <param name="type"></param>
    /// <param name="title"></param>
    /// <param name="content"></param>
    public async Task AddLogAsync(EnumLogType type, string title, string content)
    {
        try
        {
            await _freeSql.Insert<CustomLog>()
                .AppendData(new CustomLog()
                {
                    Type = type,
                    Title = title,
                    Content = content
                })
                .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加日志失败");
        }
    }

    #endregion
}
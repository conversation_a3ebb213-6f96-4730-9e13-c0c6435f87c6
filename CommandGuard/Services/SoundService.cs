using System.Media;
using CommandGuard.Interfaces;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 音效服务实现类
/// </summary>
public class SoundService : ISoundService, IDisposable
{
    #region 设置音效文件路径

    private SoundPlayer MediaStartService { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/StartService.wav" };
    private SoundPlayer MediaStopService { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/StopService.wav" };
    private SoundPlayer MediaRechargeWithdrawal { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/RechargeWithdrawal.wav" };
    private SoundPlayer MediaBetSuccess { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/BetSuccess.wav" };
    private SoundPlayer MediaBetFalse { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/BetFalse.wav" };
    private SoundPlayer MediaOpenDraw { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/OpenDraw.wav" };
    private SoundPlayer MediaStopBet { get; } = new() { SoundLocation = AppDomain.CurrentDomain.BaseDirectory + "/media/StopBet.wav" };

    #endregion

    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// 记录音效操作的详细信息，便于调试和监控
    /// </summary>
    private readonly ILogger<SoundService> _logger;

    /// <summary>
    /// 资源释放标志
    /// 标识当前实例是否已被释放，避免重复释放
    /// </summary>
    private bool _disposed;

    #endregion

    #region 构造函数

    /// <summary>
    /// 音效服务构造函数
    /// 通过依赖注入获取所需的服务实例
    /// </summary>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public SoundService(ILogger<SoundService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        MediaStartService.Load();
        MediaStopService.Load();
        MediaRechargeWithdrawal.Load();
        MediaBetSuccess.Load();
        MediaBetFalse.Load();
        MediaOpenDraw.Load();
        MediaStopBet.Load();
    }

    #endregion

    public async Task PlayStartServiceAsync()
    {
        await Task.Run(() => MediaStartService.Play());
        _logger.LogInformation("播放音效：启动服务");
    }

    public async Task PlayStopServiceAsync()
    {
        await Task.Run(() => MediaStopService.Play());
        _logger.LogInformation("播放音效：停止服务");
    }

    public async Task PlayRechargeWithdrawalAsync()
    {
        await Task.Run(() => MediaRechargeWithdrawal.Play());
        _logger.LogInformation("播放音效：上分下分提醒");
    }

    public async Task PlayBetSuccessAsync()
    {
        await Task.Run(() => MediaBetSuccess.Play());
        _logger.LogInformation("播放音效：飞单成功");
    }

    public async Task PlayBetFalseAsync()
    {
        await Task.Run(() => MediaBetFalse.Play());
        _logger.LogInformation("播放音效：飞单失败");
    }

    public async Task PlayOpenDrawAsync()
    {
        await Task.Run(() => MediaOpenDraw.Play());
        _logger.LogInformation("播放音效：已开奖");
    }

    public async Task PlayStopBetAsync()
    {
        await Task.Run(() => MediaStopBet.Play());
        _logger.LogInformation("播放音效：开始封盘");
    }

    #region IDisposable 实现

    /// <summary>
    /// 释放资源
    ///
    /// 功能：实现IDisposable接口，确保资源正确释放
    ///
    /// 执行逻辑：
    /// - 检查是否已释放
    /// - 调用异步释放方法
    /// - 标记为已释放
    /// - 抑制终结器调用
    ///
    /// 设计模式：
    /// - 标准的IDisposable实现模式
    /// - 防止重复释放
    /// - 支持垃圾回收优化
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 同步调用异步释放方法
            // 注意：这里使用GetAwaiter().GetResult()是为了在Dispose中调用异步方法
            // 在实际应用中，建议优先使用异步的DisposeSoundServiceAsync方法
            // DisposeSoundServiceAsync().GetAwaiter().GetResult();

            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }

    #endregion
}
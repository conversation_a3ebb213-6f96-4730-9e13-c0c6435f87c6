using System.Text;
using AiHelper;
using CommandGuard.Configuration;
using CommandGuard.Constants;
using CommandGuard.Enums;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

public class RobotService : IRobotService
{
    #region 私有字段和依赖

    /// <summary>
    /// 日志记录器
    ///
    /// 功能：记录服务运行过程中的详细信息
    /// 日志级别：
    /// - Debug：缓存命中、状态检查等调试信息
    /// - Information：状态变化、数据创建等重要操作
    /// - Warning：异常恢复、数据不一致等警告
    /// - Error：数据库错误、计算异常等错误信息
    ///
    /// 日志内容：
    /// - 期号数据的创建和更新
    /// - 状态变化的详细记录
    /// - 性能指标和缓存命中率
    /// - 异常情况和恢复过程
    /// </summary>
    private readonly ILogger<RobotService> _logger;

    /// <summary>
    /// 数据库服务接口
    ///
    /// 功能：提供数据库访问能力和连接管理
    /// 核心能力：
    /// - FreeSql ORM实例访问
    /// - 数据库连接池管理
    /// - 事务处理支持
    /// - 线程安全的数据库操作锁
    ///
    /// 使用场景：
    /// - 期号数据的批量创建和查询
    /// - 数据库结构的自动同步
    /// - 复杂查询和数据统计
    /// - 数据库连接异常的处理
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 聊天服务接口
    /// </summary>
    private readonly IChatService _chatService;

    private readonly IIssueTimeService _issueTimeService;

    /// <summary>
    /// 自定义日志服务接口
    /// </summary>
    private readonly ICustomLogService _customLogService;

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 开奖服务构造函数
    /// </summary>
    /// <param name="logger">日志记录器，用于记录服务运行状态和异常信息</param>
    /// <param name="freeSql"></param>
    /// <param name="chatService"></param>
    /// <param name="issueTimeService"></param>
    /// <param name="customLogService"></param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public RobotService(
        ILogger<RobotService> logger,
        IFreeSql freeSql,
        IChatService chatService,
        IIssueTimeService issueTimeService,
        ICustomLogService customLogService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _chatService = chatService ?? throw new ArgumentNullException(nameof(chatService));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        _customLogService = customLogService ?? throw new ArgumentNullException(nameof(customLogService));
    }

    #endregion

    #region 0.查询指令说明

    /// <summary>
    /// 处理查询指令
    /// </summary>
    private async Task 指令说明Handler(Member member)
    {
        try
        {
            string returnMsg = "\r";
            returnMsg += "0:指令说明\r";
            returnMsg += "1:查询积分及当前答题详情\r";
            returnMsg += "2:路子图\r";
            returnMsg += "3:答案图\r";
            returnMsg += "4:回水\r";
            returnMsg += "5:流水详情\r";
            returnMsg += "6:查询时间\r";
            returnMsg += "7:撤销答题\r";
            returnMsg += "8:查询最后封卷详情\r";
            returnMsg += "9:查询最后结算详情\r";
            returnMsg += "其它指令请咨询管理员\r";

            // Debug.WriteLine($@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]开始发送返回消息");
            await _chatService.SendGroupMessageAsync(returnMsg, member.Account);
        }
        catch (Exception ex)
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "指令说明HandlerError" + Ai.中括号右, ex.ToString());
            _logger.LogError(ex, "指令说明HandlerError");
        }
    }

    #endregion

    #region 1.处理查询指令

    /// <summary>
    /// 处理查询指令
    /// </summary>
    /// <param name="member"></param>
    private async Task 查分指令Handler(Member member)
    {
        try
        {
            // MemberInfo memberInfo = GetMemberDetail(member).Result;
            // string betDetail = string.Empty;
            //
            // // 获取当前期投注明细
            // List<BetOrder> betOrderList = await GetBetOrderList(member.Account, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
            //
            // // 取出所有投注类型
            // HashSet<EnumBetLottery> betLotteryList = [.. betOrderList.Select(betOrder => betOrder.BetLottery)];
            // betLotteryList = [.. betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];
            //
            // // 遍历所有投注类型
            // foreach (EnumBetLottery betLottery in betLotteryList)
            // {
            //     betDetail += $"{Ai.中括号左}{betLottery}{Ai.中括号右}:\r";
            //     Dictionary<string, decimal> betContentMoneyDic = CommonHelper.PlayContentList.ToDictionary(playType => playType, _ => (decimal)0);
            //     foreach (var betOrder in betOrderList.Where(order => order.BetLottery == betLottery))
            //     {
            //         betContentMoneyDic[betOrder.Con] += betOrder.Money;
            //     }
            //
            //     // 拼接投注明细
            //     foreach (var kv in betContentMoneyDic.Where(kv => kv.Value > 0))
            //     {
            //         betDetail += $"{kv.Key}/{kv.Value}\r";
            //     }
            // }
            //
            // // 拼接返回消息
            // StringBuilder returnMsgBuilder = new StringBuilder();
            // returnMsgBuilder.Append($"现积分{CommonHelper.GetFaceIdMoneyBag()}: {Math.Round(member.Balance, 2)}\r");
            // returnMsgBuilder.Append($"可回水{CommonHelper.GetFaceIdWater()}: {Math.Round(memberInfo.未回流水 / 100 * member.回水比例, 2)}\r");
            // returnMsgBuilder.Append($"现结算{CommonHelper.GetFaceIdMoneyBag()}: {Math.Round(memberInfo.总盈亏, 2)}\r");
            // if (!string.IsNullOrEmpty(betDetail))
            // {
            //     returnMsgBuilder.Append("本期答题:\r");
            //     returnMsgBuilder.Append(betDetail);
            // }
            // else
            // {
            //     returnMsgBuilder.Append($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}查无答题记录");
            // }
            //
            // string returnMsg = returnMsgBuilder.ToString();
            //
            // // Debug.WriteLine($@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]开始发送返回消息");
            // await _chatService.SendGroupMessageAsync(returnMsg, member.Account);
        }
        catch (Exception ex)
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "查分指令HandlerError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 2.发送路子图

    /// <summary>
    /// 发送路子图
    /// </summary>
    public async Task 发送路子图Handler()
    {
        await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "发送路子图Handler" + Ai.中括号右, "发送路子图Handler");
        if (UserSetting.Current.SendImageRows7)
        {
            await _chatService.SendImageAsync(ImageConstants.TanRows7ImagePath);
        }

        if (UserSetting.Current.SendImageRows6)
        {
            await Task.Delay(500);
            await _chatService.SendImageAsync(ImageConstants.TanRows6ImagePath);
        }
    }

    /// <summary>
    /// 发送路子图
    /// </summary>
    public async Task 发送路子图FullHandler()
    {
        await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "发送路子图FullHandler" + Ai.中括号右, "发送路子图FullHandler");
        await Task.Delay(0);
        if (UserSetting.Current.SendImageRows7)
        {
            await _chatService.SendImageAsync(ImageConstants.TanRows77ImagePath);
        }

        if (UserSetting.Current.SendImageRows6)
        {
            await Task.Delay(500);
            await _chatService.SendImageAsync(ImageConstants.TanRows66ImagePath);
        }
    }

    #endregion

    #region 3.发送开奖图

    /// <summary>
    /// 发送开奖图
    /// </summary>
    public async Task 发送开奖图Handler()
    {
        await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "发送开奖图Handler" + Ai.中括号右, "发送开奖图Handler");
        await _chatService.SendImageAsync(ImageConstants.DrawImagePath);
    }

    #endregion

    #region 6.发送时间

    /// <summary>
    /// 发送时间
    /// </summary>
    /// <param name="member"></param>
    public async Task 查询时间指令Handler(Member? member)
    {
        try
        {
            // 获取当前期号
            IssueTime? issueTime = _issueTimeService.GetCurrentCachedIssueTime();

            // 计算时间差
            var tmpOpenCountDown = _issueTimeService.GetCurrentCachedOpenTimeSpan();
            var tmpCloseCountDown = _issueTimeService.GetCurrentCachedCloseTimeSpan();

            string tmpCloseTimeTips;
            if (tmpCloseCountDown > 0)
            {
                string tmpHourStr;
                string tmpMinuteStr;
                string tmpSecondStr;

                int tmpHour = tmpCloseCountDown / 3600;
                int tmpMinute = tmpCloseCountDown / 60;
                int tmpSecond = tmpCloseCountDown % 60;
                if (tmpHour == 0)
                {
                    tmpHourStr = "00";
                }
                else
                {
                    tmpHourStr = tmpHour.ToString();
                    tmpHourStr = tmpHourStr.Length == 1 ? "0" + tmpHourStr : tmpHourStr;
                }

                if (tmpMinute == 0)
                {
                    tmpMinuteStr = "00";
                }
                else
                {
                    tmpMinuteStr = tmpMinute.ToString();
                    tmpMinuteStr = tmpMinuteStr.Length == 1 ? "0" + tmpMinuteStr : tmpMinuteStr;
                }

                if (tmpSecond == 0)
                {
                    tmpSecondStr = "00";
                }
                else
                {
                    tmpSecondStr = tmpSecond.ToString();
                    tmpSecondStr = tmpSecondStr.Length == 1 ? "0" + tmpSecondStr : tmpSecondStr;
                }

                tmpCloseTimeTips = string.Concat(tmpHourStr, ":", tmpMinuteStr, ":", tmpSecondStr);
            }
            else
            {
                tmpCloseTimeTips = "已封盘";
            }

            string tmpOpenTimeTips;
            if (tmpOpenCountDown > 0)
            {
                string tmpHourStr;
                string tmpMinuteStr;
                string tmpSecondStr;

                int tmpHour = tmpOpenCountDown / 3600;
                int tmpMinute = tmpOpenCountDown / 60;
                int tmpSecond = tmpOpenCountDown % 60;
                if (tmpHour == 0)
                {
                    tmpHourStr = "00";
                }
                else
                {
                    tmpHourStr = tmpHour.ToString();
                    tmpHourStr = tmpHourStr.Length == 1 ? "0" + tmpHourStr : tmpHourStr;
                }

                if (tmpMinute == 0)
                {
                    tmpMinuteStr = "00";
                }
                else
                {
                    tmpMinuteStr = tmpMinute.ToString();
                    tmpMinuteStr = tmpMinuteStr.Length == 1 ? "0" + tmpMinuteStr : tmpMinuteStr;
                }

                if (tmpSecond == 0)
                {
                    tmpSecondStr = "00";
                }
                else
                {
                    tmpSecondStr = tmpSecond.ToString();
                    tmpSecondStr = tmpSecondStr.Length == 1 ? "0" + tmpSecondStr : tmpSecondStr;
                }

                tmpOpenTimeTips = string.Concat(tmpHourStr, ":", tmpMinuteStr, ":", tmpSecondStr);
            }
            else
            {
                tmpOpenTimeTips = "已封盘";
            }

            // 构造返回消息
            StringBuilder tmpReturnMsgBuilder = new StringBuilder();
            tmpReturnMsgBuilder.Append($@"当前题号:{Ai.中括号左}{issueTime.Issue}{Ai.中括号右}" + "\r");
            tmpReturnMsgBuilder.Append($@"距离封卷:{Ai.中括号左}{tmpCloseTimeTips}{Ai.中括号右}" + "\r");
            tmpReturnMsgBuilder.Append($@"预计开将:{Ai.中括号左}{tmpOpenTimeTips}{Ai.中括号右}");
            string returnMsg = tmpReturnMsgBuilder.ToString();
            if (member == null)
            {
                await _chatService.SendGroupMessageAsync(returnMsg);
            }
            else
            {
                await _chatService.SendGroupMessageAsync(returnMsg, member.Account);
            }
        }
        catch (Exception ex)
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, "捕获 查询时间 错误:", "捕获 查询时间 错误:" + ex);
        }
    }

    #endregion

    // ##################################

    #region 发送开盘提醒

    /// <summary>
    /// 发送开盘提醒
    /// </summary>
    /// <param name="issue"></param>
    public async Task 发送开盘提醒(string issue)
    {
        string returnMsg = string.Empty;
        returnMsg = string.Concat(returnMsg, Ai.中括号左, issue, Ai.中括号右, "开★始★答★题");
        await _chatService.SendGroupMessageAsync(returnMsg);
    }

    #endregion
}
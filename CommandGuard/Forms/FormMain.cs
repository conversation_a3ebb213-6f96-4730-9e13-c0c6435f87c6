using System.Diagnostics;
using AiHelper;
using CommandGuard.Configuration;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Flurl.Http;
using Microsoft.Extensions.Logging;
using Sunny.UI;

namespace CommandGuard.Forms;

public partial class FormMain : Form
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// 记录用户操作和系统事件，便于调试和监控
    /// </summary>
    private readonly ILogger<FormMain> _logger;

    private readonly IHttpListenerService _httpListenerService;

    /// <summary>
    /// 人员业务服务
    /// 提供人员数据的CRUD操作功能
    /// </summary>
    private readonly IMemberService _memberService;

    private readonly IChatService _chatService;
    private readonly IRobotService _robotService;

    /// <summary>
    /// 期号时间服务
    /// 提供期号时间管理和监控功能
    /// </summary>
    private readonly IIssueTimeService _issueTimeService;

    /// <summary>
    /// 抽奖服务
    /// </summary>
    private readonly IDrawService _drawService;

    /// <summary>
    /// 图片服务
    /// 提供图片管理和处理功能
    /// </summary>
    private readonly IImageService _imageService;

    /// <summary>
    /// 音效服务
    /// 提供音效播放和管理功能
    /// </summary>
    private readonly ISoundService _soundService;

    /// <summary>
    /// 自定义日志服务
    /// </summary>
    private readonly ICustomLogService _customLogService;

    /// <summary>
    /// 取消令牌源，用于控制后台任务的取消
    /// </summary>
    private CancellationTokenSource? _cancellationTokenSource = new();

    #endregion

    #region 构造函数

    public FormMain(ILogger<FormMain> logger,
        IHttpListenerService httpListenerService,
        IChatService chatService,
        IRobotService robotService,
        IMemberService memberService,
        IIssueTimeService issueTimeService,
        IDrawService drawService,
        IImageService imageService,
        ISoundService soundService,
        ICustomLogService customLogService)
    {
        // 初始化窗体组件（由设计器生成）
        InitializeComponent();

        // 依赖注入的服务实例
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _httpListenerService = httpListenerService ?? throw new ArgumentNullException(nameof(httpListenerService));
        _memberService = memberService ?? throw new ArgumentNullException(nameof(memberService));
        _chatService = chatService ?? throw new ArgumentNullException(nameof(chatService));
        _robotService = robotService ?? throw new ArgumentNullException(nameof(robotService));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        _drawService = drawService ?? throw new ArgumentNullException(nameof(drawService));
        _imageService = imageService ?? throw new ArgumentNullException(nameof(imageService));
        _soundService = soundService ?? throw new ArgumentNullException(nameof(soundService));
        _customLogService = customLogService ?? throw new ArgumentNullException(nameof(customLogService));
        _logger.LogInformation("初始化完成");

        // 开启服务标记
        CommonHelper.ServiceParamDic.TryAdd(CommonHelper.IsStartService, false);
    }

    #endregion

    #region 窗体事件处理

    /// <summary>
    /// 窗体加载事件处理程序
    /// 加载数据并启动后台任务
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation("主窗体开始加载");
            List<Member> memberList = await _memberService.GetAllMemberAsync();
            dataGridView1.DataSource = memberList;

            _logger.LogInformation("开始启动服务");
            await _issueTimeService.CreateIssueTimeAsync(DateTime.Now);
            await Task.Run(() => _drawService.ClearDrawAsync(_cancellationTokenSource!.Token));
            _ = Task.Run(() => _httpListenerService.ListeningAsync(_cancellationTokenSource!.Token));
            _ = Task.Run(() => _httpListenerService.ProcessMessageQueueAsync(_cancellationTokenSource!.Token));
            _ = Task.Run(() => _issueTimeService.UpdateCurrentIssueTimeAsync(_cancellationTokenSource!.Token));
            _ = Task.Run(() => ServiceAsync(_cancellationTokenSource!.Token));
            _ = Task.Run(() => ShowTimeInfo(_cancellationTokenSource!.Token));
            _logger.LogInformation("主窗体加载完成,开始启动服务");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $@"主窗体加载时发生异常,{ex}");
            MessageBox.Show($@"加载数据时发生错误: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 开始服务按钮点击事件处理程序
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_StartService_Click(object sender, EventArgs e)
    {
        try
        {
            // if (string.IsNullOrWhiteSpace(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            // {
            //     await DbHelper.AddLogAsync(EnumLogType.机器人, "拦截开始服务", "飞单平台未登录.");
            //     UIMessageBox.ShowMessageDialog("请先登录飞单平台后再开始考试!", "操作提醒:", false, UIStyle.Red, true);
            //     return;
            // }

            try
            {
                // 先禁用按钮
                button_StartService.Enabled = false;

                // 判断是否已经指定考试群
                if (comboBox_WorkGroupId.SelectedIndex <= 0)
                {
                    await _customLogService.AddLogAsync(EnumLogType.机器人, "拦截开始服务", "未指定考试群");
                    UIMessageBox.ShowMessageDialog("请先指定考试群再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                    button_StartService.Enabled = true;
                    return;
                }

                // 判断是否已经链接通讯App
                if (!RobotHelper.GroupDic.Any())
                {
                    await _customLogService.AddLogAsync(EnumLogType.机器人, "拦截开始服务", "未指定考试群");
                    UIMessageBox.ShowMessageDialog("请先登录通讯App后再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                    return;
                }

                // 设置机器人参数
                RobotHelper.WorkGroupId = Ai.GetTextMiddle(comboBox_WorkGroupId.Text.Trim(), Ai.中括号左, Ai.中括号右);
                await _soundService.PlayStartServiceAsync();

                // 判断是否发送开奖数据图
                if (UserSetting.Current.是否发送开奖图)
                {
                    await _robotService.发送开奖图Handler();
                }

                // 判断是否发送开奖路子图
                if (UserSetting.Current.是否发送路子图)
                {
                    await Task.Delay(500);
                    await _robotService.发送路子图Handler();
                }

                // // 发送数据注意提醒
                // if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果2) && DateTime.Now < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 7, 5, 0))
                // {
                //     await ChatHelper.SendGroupMessage($"【请注意】以上是昨天最后一期的开奖结果！");
                // }

                // 发送开始服务提示
                await Task.Delay(1000);
                await _chatService.SendGroupMessageAsync("【开始服务】" + "\r" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                Thread.Sleep(1000);

                // 发送当前时间
                await _robotService.查询时间指令Handler(null);
                await _chatService.SendGroupMessageAsync("【请等待开始答题】");

                // 发送开盘提醒
                IssueTime? issueTime = _issueTimeService.GetCurrentCachedIssueTime();
                if (RobotHelper.OpenTipsList.Contains(issueTime!.Issue))
                {
                    await Task.Delay(500);
                    await _robotService.发送开盘提醒(issueTime.Issue);
                }

                // 记录进数据库
                await _customLogService.AddLogAsync(EnumLogType.机器人, "启动服务", "当前考试群为:" + Ai.中括号左 + RobotHelper.WorkGroupId + Ai.中括号右);
                comboBox_WorkGroupId.Enabled = false;
                //uiButton_Service.Text = @"停止服务";

                // 开始服务
                CommonHelper.ServiceParamDic[CommonHelper.IsStartService] = true;
                CommonHelper.StartServiceTime = DateTime.Now;

                // 设置按钮状态
                button_StartService.Enabled = false;
                button_StopService.Enabled = true;
                button_StartBet.Enabled = true;
            }
            catch (Exception ex)
            {
                await _customLogService.AddLogAsync(EnumLogType.机器人, "启动服务失败", ex.ToString());
                _logger.LogError(ex.ToString());
            }
        }
        catch (Exception ex)
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, "启动服务失败", ex.ToString());
            _logger.LogError(ex.ToString());
        }

        // try
        // {
        //     await _soundService.PlayStartServiceAsync();
        // }
        // catch (Exception ex)
        // {
        //     await _customLogService.AddLogAsync(EnumLogType.机器人, "启动服务失败", ex.ToString());
        //     _logger.LogError(ex.ToString());
        // }
    }

    /// <summary>
    /// 停止服务按钮点击事件处理程序
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_StopService_Click(object sender, EventArgs e)
    {
        try
        {
            await _soundService.PlayStopServiceAsync();
        }
        catch (Exception ex)
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, "停止服务失败", ex.ToString());
            _logger.LogError(ex.ToString());
        }
    }

    /// <summary>
    /// 窗体关闭事件处理程序
    /// 清理资源并取消后台任务
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
    {
        // 清理自定义资源
        CleanupResources();
    }

    #endregion

    /// <summary>
    /// ServiceAsync
    /// </summary>
    /// <param name="token"></param>
    private async Task ServiceAsync(CancellationToken token)
    {
        try
        {
            long whileCount = 0;
            Debug.WriteLine(whileCount);
            while (!token.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(1000, token);
                    whileCount++;

                    #region Robot 相关操作

                    // 判断获取Robot账号信息
                    if (string.IsNullOrEmpty(RobotHelper.RobotInfo.Account))
                    {
                        await _chatService.GetRobotInfoAsync();
                        continue;
                    }

                    // 判断获取Robot群列表
                    if (RobotHelper.GroupDic.Count.Equals(0))
                    {
                        await _chatService.GetGroupDicAsync();
                        continue;
                    }

                    #endregion

                    // 把游戏种类传递给平台端,平台端根据游戏种类获取对应开奖信息
                    try
                    {
                        string url = $"{RobotSetting.Current.PlatformHost}/GameInfo/";
                        await url.WithTimeout(TimeSpan.FromSeconds(3))
                            .PostJsonAsync(new
                            {
                                CommonHelper.Lottery
                            }, cancellationToken: token);
                    }
                    catch (Exception ex)
                    {
                        await _customLogService.AddLogAsync(EnumLogType.机器人, "传递游戏种类Error", ex.ToString());
                    }

                    // 获取开奖数据
                    bool hasNewKaiJiangData = await _drawService.GetDrawFromApiAsync(token);

                    // 判断是否有新开奖数据
                    if (hasNewKaiJiangData)
                    {
                        // 获取最新开奖数据
                        KaiJiang lastKj = await _drawService.GetDrawLastAsync(token);
                        await AddOpenResultToLog(lastKj);

                        // 生成开奖结果图片和摊路图
                        await _imageService.DrawOpenDataAsync(lastKj);
                        await _imageService.DrawTanImageAsync(lastKj, 7);
                        await _imageService.DrawTanImageAsync(lastKj, 6);
                        await _imageService.DrawTanImageFullAsync(lastKj, 7);
                        await _imageService.DrawTanImageFullAsync(lastKj, 6);
                    }
                }
                catch (Exception ex)
                {
                    await _customLogService.AddLogAsync(EnumLogType.机器人, "DoWork异常", ex.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            await _customLogService.AddLogAsync(EnumLogType.机器人, "DoWork异常", ex.ToString());
        }
    }

    /// <summary>
    /// 添加开奖结果到日志
    /// </summary>
    /// <param name="lastKj"></param>
    private async Task AddOpenResultToLog(KaiJiang lastKj)
    {
        try
        {
            // 根据不同彩种计算开奖结果并记录日志
            if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果))
            {
                // 宾果1玩法：取最后一个号码进行番摊计算
                string drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果1);
                int drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                int result = await RobotHelper.GetDrawResult(drawNumSum);
                await _customLogService.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}宾果1{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 宾果2玩法：取第1、倒数第2、倒数第1个号码进行番摊计算
                drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果2);
                drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                result = await RobotHelper.GetDrawResult(drawNumSum);
                await _customLogService.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}宾果2{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 宾果3玩法：所有号码总和进行番摊计算
                drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果3);
                drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                result = await RobotHelper.GetDrawResult(drawNumSum);
                await _customLogService.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}宾果3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");
            }
            else if (CommonHelper.Lottery.Equals(EnumLottery.一六八飞艇))
            {
                // 飞艇前3玩法：取第1、2、3位号码进行番摊计算
                string drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.一六八飞艇前3);
                int drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                int result = await RobotHelper.GetDrawResult(drawNumSum);
                await _customLogService.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}飞艇前3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 飞艇中3玩法：取第4、5、6位号码进行番摊计算
                drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.一六八飞艇中3);
                drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                result = await RobotHelper.GetDrawResult(drawNumSum);
                await _customLogService.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}飞艇中3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");

                // 飞艇后3玩法：取第8、9、10位号码进行番摊计算
                drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.一六八飞艇后3);
                drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                result = await RobotHelper.GetDrawResult(drawNumSum);
                await _customLogService.AddLogAsync(EnumLogType.平台, $"{Ai.中括号左}飞艇后3{Ai.中括号右}",
                    $"{Ai.中括号左}{lastKj.Issue}{Ai.中括号右}开{Ai.中括号左}{result}{Ai.中括号右}{Ai.中括号左}{Ai.GetTextRight(lastKj.Time, " ")}{Ai.中括号右}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.ToString());
        }
    }

    /// <summary>
    /// // 显示当前时间信息
    /// </summary>
    /// <param name="token"></param>
    private async Task ShowTimeInfo(CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(1000, token);

                // 从IssueTimeService中获取当前期号信息
                var issueTime = _issueTimeService.GetCurrentCachedIssueTime();
                if (issueTime is null)
                {
                    continue;
                }

                // 计算时间差
                // DateTime now = DateTime.Now;
                var openTimeSpan = _issueTimeService.GetCurrentCachedOpenTimeSpan();
                var closeTimeSpan = _issueTimeService.GetCurrentCachedCloseTimeSpan();

                // 根据状态显示不同内容
                if (openTimeSpan < 0 && closeTimeSpan > 0)
                {
                    Invoke(() =>
                    {
                        label_正在投注期数标题.Text = @"正在投注期数";
                        label_正在投注期数.Text = $@"{issueTime.Issue}";
                        label_封盘倒计时.Text = FormatTimeRemaining(closeTimeSpan);
                    });
                }
                else
                {
                    Invoke(() =>
                    {
                        label_正在投注期数标题.Text = @"即将开盘";
                        label_正在投注期数.Text = $@"{issueTime.Issue}";
                        label_封盘倒计时.Text = FormatTimeRemaining(openTimeSpan);
                    });
                }

                // 加载群列表
                Invoke(() =>
                {
                    if (comboBox_WorkGroupId.Items.Count.Equals(0) && RobotHelper.GroupDic.Any())
                    {
                        comboBox_WorkGroupId.Items.Add("选择聊天群");
                        foreach (KeyValuePair<string, string> kv in RobotHelper.GroupDic)
                        {
                            comboBox_WorkGroupId.Items.Add($"{Ai.中括号左}{kv.Key}{Ai.中括号右}{kv.Value}");
                        }

                        comboBox_WorkGroupId.SelectedIndex = 0;
                    }
                });

                // 获取最新开奖数据
                if (!await _drawService.HasDrawAsync(token))
                {
                    continue;
                }

                // 显示最新开奖数据
                KaiJiang lastKj = await _drawService.GetDrawLastAsync(token);
                Invoke(() => { label_开奖期数.Text = lastKj.Issue; });
                if (CommonHelper.Lottery is EnumLottery.台湾宾果)
                {
                    if (UserSetting.Current.启用台湾宾果1.Equals(1))
                    {
                        string drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果1);
                        int drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                        int result = await RobotHelper.GetDrawResult(drawNumSum);
                        Invoke(() =>
                        {
                            label_开奖号码.Text = (drawNumSum % 100).ToString();
                            label_番摊结果.Text = result.ToString();
                        });
                    }
                    else if (UserSetting.Current.启用台湾宾果2.Equals(1))
                    {
                        string drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果2);
                        int drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                        int result = await RobotHelper.GetDrawResult(drawNumSum);
                        Invoke(() =>
                        {
                            label_开奖号码.Text = (drawNumSum % 100).ToString();
                            label_番摊结果.Text = result.ToString();
                        });
                    }
                    else if (UserSetting.Current.启用台湾宾果3.Equals(1))
                    {
                        string drawNumStr = await RobotHelper.GetDrawNumStr(lastKj, EnumBetLottery.台湾宾果3);
                        int drawNumSum = await RobotHelper.GetDrawNumSum(drawNumStr);
                        int result = await RobotHelper.GetDrawResult(drawNumSum);
                        Invoke(() =>
                        {
                            label_开奖号码.Text = (drawNumSum % 100).ToString();
                            label_番摊结果.Text = result.ToString();
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
                await Task.Delay(0, token);
            }
        }
    }

    /// <summary>
    /// 格式化时间显示
    /// </summary>
    /// <param name="totalSeconds">总秒数</param>
    /// <returns>格式化的时间字符串</returns>
    private string FormatTimeRemaining(int totalSeconds)
    {
        if (totalSeconds <= 0)
        {
            return "00:00:00";
        }

        // 大于等于1小时
        if (totalSeconds >= 3600)
        {
            var hours = (totalSeconds / 3600).ToString("D2");
            var minutes = ((totalSeconds % 3600) / 60).ToString("D2");
            var seconds = (totalSeconds % 60).ToString("D2");
            return $"{hours}:{minutes}:{seconds}";
        }

        // 大于等于1分钟
        if (totalSeconds >= 60)
        {
            var minutes = (totalSeconds / 60).ToString("D2");
            var seconds = (totalSeconds % 60).ToString("D2");
            return $"00:{minutes}:{seconds}";
        }

        // 小于1分钟
        return $"00:00:{totalSeconds:D2}";
    }

    #region 资源清理

    /// <summary>
    /// 清理自定义资源
    /// 在窗体关闭时调用，用于清理取消令牌等资源
    /// </summary>
    private void CleanupResources()
    {
        try
        {
            _logger.LogDebug("开始清理应用程序资源");

            // 取消并释放取消令牌源
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            _logger.LogDebug("取消令牌已清理");

            // 清理Service层的缓存
            _issueTimeService.ClearCache();
            _logger.LogDebug("服务缓存已清理");

            _logger.LogDebug("应用程序资源清理完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理资源时发生错误");
        }
    }

    #endregion
}
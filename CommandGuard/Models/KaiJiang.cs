using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 开奖信息模型类 - 彩票开奖数据的核心存储模型
/// </summary>
[Table(Name = "KaiJiang")]
[Index("IX_KaiJiang_Issue", nameof(Issue), true)]
public class KaiJiang
{
    /// <summary>
    /// 开奖记录唯一标识 - 数据库自增主键
    ///
    /// 特点：
    /// - 长整型确保足够的ID空间
    /// - 自动递增，无需手动设置
    /// - 作为开奖记录的唯一标识符
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 开奖期号 - 彩票期号的唯一标识
    ///
    /// 格式规范：
    /// - 台湾宾果：YYYYMMDD-XXX（如：20241211-001）
    /// - 一六八飞艇：YYYYMMDD-XXX（如：20241211-001）
    /// - 新一六八XL：YYYYMMDD-XXX（如：20241211-001）
    ///
    /// 业务用途：
    /// - 关联投注订单进行结算
    /// - 按期号查询开奖历史
    /// - 生成开奖统计报表
    /// - 确保开奖数据的唯一性
    ///
    /// 重要性：
    /// - 作为投注和开奖的关联键
    /// - 防止重复开奖数据
    /// - 支持历史数据查询
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 开奖号码 - 彩票开奖的具体号码序列
    ///
    /// 格式说明：
    /// - 使用逗号分隔的数字序列
    /// - 台湾宾果：21个号码（如：01,02,03,...,19,20,21）
    /// - 一六八飞艇：10个号码（如：01,02,03,04,05,06,07,08,09,10）
    /// - 新一六八XL：10个号码（如：01,02,03,04,05,06,07,08,09,10）
    ///
    /// 数据处理：
    /// - 通过RobotHelper.GetDrawNum()提取特定位置号码
    /// - 根据不同彩种规则计算投注结果
    /// - 支持各种投注玩法的结果计算
    ///
    /// 示例数据：
    /// - 台湾宾果："01,15,08,20,03,11,17,09,14,06,19,02,12,18,05,16,10,21,07,13,04"
    /// - 飞艇系列："01,05,08,03,09,02,07,04,10,06"
    ///
    /// 重要性：
    /// - 所有投注结算的数据基础
    /// - 生成开奖图片的数据源
    /// - 路子图分析的原始数据
    /// </summary>
    public string DrawNum { get; set; } = string.Empty;

    /// <summary>
    /// 开奖时间 - 彩票开奖的具体时间
    ///
    /// 格式：
    /// - 标准日期时间字符串
    /// - 通常包含日期和具体时间
    /// - 示例："2024-12-11 14:35:00"
    ///
    /// 用途：
    /// - 显示在开奖图片上
    /// - 开奖历史查询和排序
    /// - 验证开奖数据的时效性
    /// - 生成开奖时间统计
    ///
    /// 数据来源：
    /// - 从平台API获取的官方开奖时间
    /// - 确保时间的准确性和权威性
    ///
    /// 业务意义：
    /// - 用户查看开奖历史的重要信息
    /// - 系统日志和审计的时间依据
    /// - 开奖图片展示的时间信息
    /// </summary>
    public string Time { get; set; } = string.Empty;
}
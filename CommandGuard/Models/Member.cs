using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 会员信息模型类 - 彩票机器人系统的用户管理核心模型
///
/// 功能说明：
/// - 存储系统中所有用户的基本信息和账户数据
/// - 支持用户身份识别、余额管理、层级关系
/// - 提供完整的用户权限和属性管理
/// - 支持拉手返利和回水机制
///
/// 核心功能：
/// 1. 用户身份管理：账号、昵称、备注名
/// 2. 财务管理：余额、回水比例
/// 3. 层级关系：拉手上级、返利比例
/// 4. 用户分类：真人用户、假人用户
///
/// 业务场景：
/// - 用户注册和信息管理
/// - 投注时的身份验证和余额扣除
/// - 上分下分的余额变动
/// - 回水返利的计算和发放
/// - 拉手层级的管理和返利
///
/// 数据库表：Member
/// - 使用Account作为主键，确保用户唯一性
/// - 支持复杂的用户查询和统计
/// - 关联BetOrder、Finance等业务表
///
/// 设计特点：
/// - 使用字符串主键，便于跨平台用户标识
/// - 支持中文字段名，贴近业务语义
/// - 完整的财务和层级关系支持
/// - 灵活的用户分类机制
///
/// 安全考虑：
/// - 余额字段使用decimal确保精度
/// - 支持假人用户的数据隔离
/// - 拉手关系的循环检测
/// </summary>
[Table(Name = "Member")]
[Index("IX_Member_Account", nameof(Account), true)]
public class Member
{
    /// <summary>
    /// 用户账号 - 用户在系统中的唯一标识
    /// </summary>
    [Column(IsPrimary = true)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称 - 用户在聊天平台上的显示名称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 备注名 - 管理员为用户设置的内部备注名称
    /// </summary>
    public string RemarkName { get; set; } = string.Empty;

    /// <summary>
    /// 账户余额 - 用户当前的可用资金余额
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 回水比例 - 用户投注的回水返还百分比
    /// </summary>
    public decimal RebatePercent { get; set; }

    /// <summary>
    /// 是否假人 - 标识用户是真实用户还是系统模拟用户
    /// </summary>
    public bool IsFake { get; set; }

    /// <summary>
    /// 是否删除 - 标识用户是否被删除
    /// </summary>
    public bool Deleted { get; set; }

    /// <summary>
    /// 拉手上级 - 推荐该用户的上级用户账号
    /// </summary>
    public string ParentAccount { get; set; } = string.Empty;

    /// <summary>
    /// 返利比例 - 上级从该用户投注中获得的返利百分比
    /// </summary>
    public decimal ParentRebatePercent { get; set; } = 0;
}
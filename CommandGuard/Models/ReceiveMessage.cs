using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 接收消息模型类 - 机器人接收到的聊天消息记录
///
/// 功能说明：
/// - 存储机器人从各聊天平台接收到的所有消息
/// - 提供消息的完整记录和处理状态跟踪
/// - 支持消息的分类处理和状态管理
/// - 便于消息历史查询和问题追踪
///
/// 业务场景：
/// - 用户在群聊中发送投注消息
/// - 用户发送上分下分申请
/// - 用户查询余额和历史记录
/// - 管理员发送系统指令
/// - 其他各种聊天交互
///
/// 处理流程：
/// 1. 聊天平台接收消息
/// 2. 机器人获取消息内容
/// 3. 创建ReceiveMessage记录
/// 4. 解析消息内容和意图
/// 5. 执行相应的业务逻辑
/// 6. 更新消息处理状态
///
/// 数据库表：ReceiveMessage
/// - 使用GUID作为主键，确保全局唯一
/// - 支持按时间、用户、群组查询
/// - 提供消息处理状态的跟踪
///
/// 重要性：
/// - 所有用户交互的数据基础
/// - 业务处理的触发源头
/// - 问题追踪的重要依据
/// - 系统运行的完整记录
/// </summary>
[Table(Name = "ReceiveMessage")]
public class ReceiveMessage
{
    /// <summary>
    /// 消息唯一标识 - 消息记录的全局唯一ID
    ///
    /// 特点：
    /// - 使用GUID确保全局唯一性
    /// - 自动生成，无需手动设置
    /// - 跨平台、跨时间的唯一标识
    ///
    /// 用途：
    /// - 消息记录的主键
    /// - 关联其他业务数据的外键
    /// - 消息追踪和问题定位
    /// - 防重复处理的依据
    ///
    /// 格式：
    /// - 标准GUID格式（如：550e8400-e29b-41d4-a716-************）
    /// - 32位十六进制数字加连字符
    /// - 确保在分布式环境下的唯一性
    /// </summary>
    [Column(IsPrimary = true)]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 接收时间 - 机器人接收到消息的系统时间
    ///
    /// 用途：
    /// - 记录消息的接收时间戳
    /// - 用于消息的时间排序
    /// - 性能分析和统计
    /// - 默认为当前系统时间
    ///
    /// 与MsgTime的区别：
    /// - ReceiveTime：机器人接收时间（系统时间）
    /// - MsgTime：消息发送时间（平台时间）
    /// - 两者可能存在时间差
    ///
    /// 业务价值：
    /// - 消息处理的时间基准
    /// - 系统性能监控指标
    /// - 消息延迟分析依据
    /// </summary>
    public DateTime ReceiveTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 消息时间 - 消息在聊天平台上的发送时间
    ///
    /// 数据来源：
    /// - 从聊天平台API获取
    /// - 平台记录的消息时间戳
    /// - 通常为字符串格式
    ///
    /// 用途：
    /// - 显示消息的真实发送时间
    /// - 用户查看消息历史
    /// - 时间相关的业务逻辑
    ///
    /// 格式特点：
    /// - 字符串类型，适应不同平台格式
    /// - 可能包含时区信息
    /// - 需要根据平台特点解析
    ///
    /// 重要性：
    /// - 用户体验的重要信息
    /// - 消息真实性的依据
    /// - 时间敏感业务的基础
    /// </summary>
    public string MsgTime { get; set; } = string.Empty;

    /// <summary>
    /// 群组标识 - 消息来源的群组ID
    ///
    /// 用途：
    /// - 标识消息来源的具体群组
    /// - 支持多群组的消息管理
    /// - 群组相关业务的基础数据
    ///
    /// 数据来源：
    /// - 从聊天平台API获取
    /// - 平台分配的群组唯一标识
    /// - 不同平台格式可能不同
    ///
    /// 业务应用：
    /// - 群组消息的分类处理
    /// - 群组权限的控制
    /// - 群组统计和分析
    ///
    /// 特点：
    /// - 字符串类型，适应不同平台
    /// - 可能为数字ID或字符串ID
    /// - 确保群组的准确识别
    /// </summary>
    public string GroupId { get; set; } = string.Empty;

    /// <summary>
    /// 群组名称 - 消息来源群组的显示名称
    ///
    /// 用途：
    /// - 提供群组的友好显示名称
    /// - 便于管理员识别和管理
    /// - 用户界面的显示信息
    ///
    /// 数据来源：
    /// - 从聊天平台API获取
    /// - 群组设置的显示名称
    /// - 可能包含中文或特殊字符
    ///
    /// 与GroupId的关系：
    /// - GroupId：唯一标识，用于系统处理
    /// - GroupName：显示名称，用于用户界面
    /// - 两者配合提供完整的群组信息
    ///
    /// 业务价值：
    /// - 提升管理界面的可读性
    /// - 便于群组的快速识别
    /// - 增强用户体验
    /// </summary>
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// 发送者账号 - 发送消息的用户账号
    ///
    /// 用途：
    /// - 标识消息的发送者
    /// - 关联Member表进行用户管理
    /// - 业务处理的用户依据
    ///
    /// 数据来源：
    /// - 从聊天平台API获取
    /// - 对应平台的用户唯一标识
    /// - 与Member.Account字段对应
    ///
    /// 业务关联：
    /// - 投注处理时的用户识别
    /// - 余额操作的目标用户
    /// - 权限验证的依据
    ///
    /// 重要性：
    /// - 所有用户相关业务的基础
    /// - 确保操作的用户准确性
    /// - 支持用户行为的追踪
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容 - 用户发送的具体消息文本
    ///
    /// 内容类型：
    /// - 投注消息：如"大/1000"、"1/500"
    /// - 申请消息：如"上分1000"、"下分500"
    /// - 查询消息：如"余额"、"历史"
    /// - 普通聊天：日常交流内容
    ///
    /// 处理流程：
    /// 1. 接收原始消息内容
    /// 2. 进行内容解析和分类
    /// 3. 提取业务相关信息
    /// 4. 执行对应的业务逻辑
    ///
    /// 数据特点：
    /// - 可能包含各种字符和符号
    /// - 需要进行格式化和验证
    /// - 支持多语言内容
    ///
    /// 业务价值：
    /// - 用户意图识别的数据源
    /// - 业务处理的核心依据
    /// - 问题追踪的重要信息
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 是否已读 - 消息的处理状态标识
    ///
    /// 状态含义：
    /// - false：未处理的新消息
    /// - true：已处理的消息
    ///
    /// 用途：
    /// - 标识消息是否已被处理
    /// - 避免重复处理相同消息
    /// - 支持消息处理状态的跟踪
    ///
    /// 处理流程：
    /// 1. 消息接收时设为false
    /// 2. 消息处理完成后设为true
    /// 3. 查询时过滤已处理消息
    ///
    /// 业务价值：
    /// - 确保消息处理的完整性
    /// - 避免重复业务操作
    /// - 提供处理状态的可视化
    /// </summary>
    public bool IsRead { get; set; } = false;

    /// <summary>
    /// 处理时间 - 消息被处理完成的时间
    ///
    /// 用途：
    /// - 记录消息处理完成的时间
    /// - 计算消息处理耗时
    /// - 性能分析和优化依据
    ///
    /// 更新时机：
    /// - 消息处理完成时更新
    /// - 与IsRead状态同步更新
    /// - 初始值为默认时间
    ///
    /// 业务价值：
    /// - 消息处理效率监控
    /// - 系统性能分析
    /// - 用户响应时间统计
    ///
    /// 计算公式：
    /// - 处理耗时 = PreTime - ReceiveTime
    /// - 用于系统性能优化
    /// </summary>
    public DateTime PreTime { get; set; }

    /// <summary>
    /// 备注信息 - 消息处理的额外备注
    ///
    /// 用途：
    /// - 记录消息处理的特殊情况
    /// - 标记异常或错误信息
    /// - 提供额外的上下文信息
    ///
    /// 内容示例：
    /// - "投注成功"
    /// - "余额不足"
    /// - "格式错误"
    /// - "系统异常"
    ///
    /// 业务价值：
    /// - 便于问题诊断和分析
    /// - 提供处理结果的详细信息
    /// - 支持客服处理用户问题
    ///
    /// 使用场景：
    /// - 异常情况的记录
    /// - 处理结果的说明
    /// - 调试信息的保存
    /// </summary>
    public string Remark { get; set; } = string.Empty;
}
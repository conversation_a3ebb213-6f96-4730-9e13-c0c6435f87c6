using CommandGuard.Enums;
using FreeSql.DataAnnotations;
using Newtonsoft.Json;

namespace CommandGuard.Models;

public class CustomLog
{
    /// <summary>
    /// 日志记录唯一标识 - 数据库自增主键
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 日志类型 - 日志记录的分类标识
    /// </summary>
    public EnumLogType Type { get; set; }

    /// <summary>
    /// 记录时间 - 日志事件发生的时间戳
    /// </summary>
    public DateTime Time { get; set; } = DateTime.Now;

    /// <summary>
    /// 日志标题 - 日志事件的简要描述
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 日志内容 - 日志事件的详细信息
    /// </summary>
    [JsonProperty]
    [Column(DbType = "nvarchar(4000)")]
    public string Content { get; set; } = string.Empty;
}
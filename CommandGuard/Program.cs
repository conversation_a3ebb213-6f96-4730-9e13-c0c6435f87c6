using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using CommandGuard.Configuration;
using CommandGuard.Forms;
using CommandGuard.Interfaces;
using CommandGuard.Services;
using FreeSql;
using Serilog;

namespace CommandGuard;

/// <summary>
/// 应用程序入口类
/// 负责应用程序的初始化、依赖注入配置和生命周期管理
/// </summary>
static class Program
{
    /// <summary>
    /// 应用程序主入口点
    /// 配置日志系统、依赖注入容器，启动主窗体
    /// </summary>
    [STAThread]
    static void Main()
    {
        try
        {
            // 首先配置Serilog日志系统，确保后续操作都能被记录
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(BuildConfiguration())
                .CreateLogger();
            Log.Information("应用程序启动开始");

            Log.Information("初始化WinForms应用程序配置");
            ApplicationConfiguration.Initialize();

            Log.Information("正在加载用户配置");
            UserSetting.Current.Load();
            Log.Information("用户配置加载完成");

            // 创建依赖注入服务容器
            ServiceCollection services = new ServiceCollection();
            Log.Information("正在配置依赖注入容器");
            ConfigureServices(services);
            Log.Information("依赖注入容器配置完成");

            // 构建服务提供者，完成所有依赖关系的解析
            ServiceProvider serviceProvider = services.BuildServiceProvider();

            // 从容器中获取主窗体实例（自动注入所有依赖）
            var formMain = serviceProvider.GetRequiredService<FormMain>();
            Log.Information("主窗体创建成功，开始运行应用程序");

            // 启动WinForms消息循环，进入应用程序主循环
            Application.Run(formMain);

            // 应用程序正常退出时释放资源
            serviceProvider.Dispose();
            Log.Information("应用程序正常退出");
        }
        catch (Exception ex)
        {
            // 记录启动失败的详细信息
            Log.Fatal(ex, "应用程序启动失败");

            // 向用户显示友好的错误消息
            MessageBox.Show($@"应用程序启动失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 确保日志系统正确关闭，刷新所有待写入的日志
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 构建应用程序配置
    /// 从appsettings.json文件加载配置信息，支持配置文件的热重载功能
    /// </summary>
    /// <returns>配置对象，包含所有应用程序设置</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory()) // 设置配置文件的基础路径为当前目录
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true) // 加载主配置文件，必需且支持热重载
            .Build();
    }

    /// <summary>
    /// 配置依赖注入服务容器
    /// 注册所有应用程序需要的服务和组件，采用不同的生命周期管理策略优化性能和资源使用
    /// </summary>
    /// <param name="services">服务集合，用于注册各种服务</param>
    private static void ConfigureServices(IServiceCollection services)
    {
        // 注册配置服务
        IConfiguration configuration = BuildConfiguration();
        services.AddSingleton(configuration);
        services.Configure<AppSettings>(options => configuration.Bind(options));

        // 注册日志服务
        // 清除默认的日志提供程序，使用Serilog作为统一的日志框架
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(); // 添加Serilog作为日志提供程序
        });

        // 配置数据库连接
        Func<IServiceProvider, IFreeSql> freeSqlFactory = _ =>
        {
            IFreeSql fSql = new FreeSqlBuilder()
                .UseConnectionString(DataType.Sqlite, configuration.GetConnectionString("DefaultConnection"))
                .UseAdoConnectionPool(true)
                .UseMonitorCommand(cmd => Console.WriteLine($@"Sql：{cmd.CommandText}"))
                .UseAutoSyncStructure(true) //自动同步实体结构到数据库，只有CRUD时才会生成表
                .Build();
            return fSql;
        };

        services.AddSingleton(freeSqlFactory); // 注入FreeSql实例工厂
        services.AddSingleton<IHttpListenerService, HttpListenerService>(); // 注册HTTP监听服务（单例生命周期）
        services.AddSingleton<IMemberService, MemberService>(); // 注册Member业务服务（作用域生命周期）
        services.AddSingleton<IIssueTimeService, IssueTimeService>(); // 注册期号时间服务（单例生命周期）
        services.AddSingleton<IImageService, ImageService>(); // 注册图片服务（作用域生命周期）
        services.AddSingleton<IDrawService, DrawService>(); // 注册开奖数据服务（作用域生命周期）
        services.AddSingleton<ISoundService, SoundService>(); // 注册音效服务（单例生命周期）
        services.AddSingleton<IChatService, ChatService>(); // 注册聊天服务（单例生命周期）
        services.AddSingleton<IRobotService, RobotService>(); // 注册机器人服务（单例生命周期）
        services.AddSingleton<ICustomLogService, CustomLogService>(); // 注册自定义日志服务（单例生命周期）
        services.AddTransient<FormMain>(); // 注册窗体（瞬态生命周期）
    }
}
using Sunny.UI;

namespace CommandGuard.Configuration;

[ConfigFile("Configuration\\UserSetting.ini")]
public class UserSetting : IniConfig<UserSetting>
{
    [ConfigSection("KingRobot")] public int SelectChatApp { get; set; }
    [ConfigSection("KingRobot")] public bool 是否发送开奖图 { get; set; }
    [ConfigSection("KingRobot")] public bool 是否发送路子图 { get; set; }
    [ConfigSection("KingRobot")] public bool 是否允许撤单 { get; set; }
    [ConfigSection("KingRobot")] public bool 是否对冲吃单 { get; set; }
    [ConfigSection("KingRobot")] public bool 是否开启飞单 { get; set; }
    [ConfigSection("KingRobot")] public bool 假人自动上分 { get; set; }
    [ConfigSection("KingRobot")] public bool 自动回水 { get; set; }
    [ConfigSection("KingRobot")] public bool SendImageRows7 { get; set; }
    [ConfigSection("KingRobot")] public bool SendImageRows6 { get; set; }
    [ConfigSection("KingRobot")] public bool AutoCancelOrder { get; set; }
    [ConfigSection("KingRobot")] public string LastBetIssue { get; set; } = string.Empty;
    [ConfigSection("KingRobot")] public int CheckBetResultCount { get; set; } = 10;

    // 其他设置
    [ConfigSection("KingRobot")] public decimal 回水比例 { get; set; } = (decimal)1.5;
    [ConfigSection("KingRobot")] public int 显示回水金额 { get; set; } = 1;
    [ConfigSection("KingRobot")] public int 开盘时间 { get; set; } = 260;
    [ConfigSection("KingRobot")] public int 封盘时间 { get; set; } = 30;
    [ConfigSection("KingRobot")] public int 最低飞单金额 { get; set; } = 10;

    [ConfigSection("KingRobot")] public int 启用台湾宾果1 { get; set; }
    [ConfigSection("KingRobot")] public int 启用台湾宾果2 { get; set; }
    [ConfigSection("KingRobot")] public int 启用台湾宾果3 { get; set; }
    [ConfigSection("KingRobot")] public int 启用168飞艇前3 { get; set; }
    [ConfigSection("KingRobot")] public int 启用168飞艇中3 { get; set; }
    [ConfigSection("KingRobot")] public int 启用168飞艇后3 { get; set; }
    [ConfigSection("KingRobot")] public int 启用新168XL前 { get; set; }
    [ConfigSection("KingRobot")] public int 启用新168XL中 { get; set; }
    [ConfigSection("KingRobot")] public int 启用新168XL后 { get; set; }
    [ConfigSection("KingRobot")] public int ImgType { get; set; }
    [ConfigSection("KingRobot")] public int ImageTitleBg { get; set; }


    /// <summary>
    /// 设置默认值
    /// </summary>
    public override void SetDefault()
    {
        base.SetDefault();
        是否发送开奖图 = true;
        是否发送路子图 = true;
        是否允许撤单 = true;
        是否对冲吃单 = false;
        是否开启飞单 = true;
        假人自动上分 = false;
        自动回水 = false;
        SendImageRows7 = true;
        SendImageRows6 = false;
        AutoCancelOrder = false;
        LastBetIssue = string.Empty;
        CheckBetResultCount = 10;
        回水比例 = (decimal)1.5;
        显示回水金额 = 1;
        开盘时间 = 260;
        封盘时间 = 30;
        最低飞单金额 = 10;
        ImgType = 1;
        ImageTitleBg = 1;
    }
}